<template>
  <div class="secondaryGroupingSelector">
    <div
      v-for="(item, index) in items"
      :key="index"
      :class="['item', { selected: selectedItem === item.value }]"
      @click="handleItemClick(item, index)"
    >
      {{ item.label }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps } from 'vue'

// 定义组件属性
interface GroupingItem {
  label: string
  value: string
}

const props = defineProps<{
  items: GroupingItem[]
}>()

// 定义事件
const emit = defineEmits<{
  (e: 'change', value: any): void
}>()

const selectedItem = ref<string>('all')

// 处理点击事件
const handleItemClick = (item: GroupingItem, index: number) => {
  selectedItem.value = item.value
  emit('change', { item, index })
}
</script>

<style lang="scss" scoped>
.secondaryGroupingSelector {
  display: flex;
  align-items: center;

  .item {
    color: #666;
    cursor: pointer;
    background-color: $gray-100;
    border-radius: 16px;
    padding: 8px 16px;
    font-size: 13px;
    line-height: 14px;
    display: inline-block;

    &:not(:first-child) {
      margin-left: $spacing-md;
    }

    &.selected {
      color: $primary-color;
      background-color: #daf7ed;
      font-weight: 600;
    }

    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
