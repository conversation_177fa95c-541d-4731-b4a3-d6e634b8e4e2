<template>
  <div class="work-order-page">
    <div class="list-action-wrapper">
      <QuickSearchTabbar :tabs="tabsData" @tab-click="handleTabClick" />
      <div class="search-row">
        <div>
          <SecondaryGroupingSelector :items="groupingItems" @change="handleGroupingChange" />
        </div>
        <div>
          <AdvancedSearchInput
            v-model="searchQuery"
            :search-options="searchOptions"
            @search="handleSearch"
            @type-change="handleSearchTypeChange"
          />
        </div>
      </div>
      <div class="filter-row">
        <AdvancedSearchForm ref="workOrderFormRef" :collapsed-items="3">
          <template #item-1>
            <a-form-item name="workOrderNo" label="计划结束时间">
              <a-input placeholder="请输入工单编号" />
            </a-form-item>
          </template>
          <template #item-2>
            <a-form-item name="workOrderNo" label="计划结束时间">
              <a-input placeholder="请输入工单编号" />
            </a-form-item>
          </template>
          <template #item-3>
            <a-form-item name="workOrderNo" label="计划结束时间">
              <a-input placeholder="请输入工单编号" />
            </a-form-item>
          </template>
          <template #item-4>
            <a-form-item name="workOrderNo" label="计划结束时间">
              <a-input placeholder="请输入工单编号" />
            </a-form-item>
          </template>
          <template #item-5>
            <a-form-item name="workOrderNo" label="计划结束时间">
              <a-input placeholder="请输入工单编号" />
            </a-form-item>
          </template>
        </AdvancedSearchForm>
      </div>
    </div>
    <div class="list-table-wrapper">
      <div class="list-table-header">
        <div class="list-table-header__left">
          <a-button type="primary" :icon="h(PlusCircleFilled)">创建工单</a-button>
          <a-button type="default" :icon="h(UploadOutlined)">导出</a-button>
          <a-button type="default" :icon="h(DownloadOutlined)">导入</a-button>
          <a-dropdown
            placement="bottomLeft"
            trigger="click"
            :overlayStyle="{ padding: 0 }"
            v-model:open="columnConfigVisible"
          >
            <a-button type="text" :icon="h(SettingOutlined)">字段配置</a-button>
            <template #overlay>
              <div class="column-config-dropdown" @click.stop>
                <ColumnConfigPanel
                  :columns="columnSettings"
                  @update:columns="handleColumnsUpdate"
                />
              </div>
            </template>
          </a-dropdown>
          <a-button type="text" :icon="h(DownloadOutlined)">排序</a-button>
          <a-button type="text" :icon="h(DownloadOutlined)">筛选</a-button>
          <a-button type="text" :icon="h(DownloadOutlined)">行高</a-button>
        </div>
        <div class="list-table-header__right">
          <a-button type="text" :icon="h(DownloadOutlined)">导入日志</a-button>
          <a-button type="text" :icon="h(DownloadOutlined)">操作记录</a-button>
        </div>
      </div>
      <div class="list-table-body">
        <a-table
          :dataSource="dataSource"
          :columns="visibleColumns"
          :pagination="{
            showTotal: (total: number) => `${total} 条记录`,
            showSizeChanger: true,
            showQuickJumper: true,
          }"
          :customRow="setRowStyle"
          :scroll="{ x: 4200, y: 400 }"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue'
import {
  ProfileFilled,
  PlusCircleFilled,
  UploadOutlined,
  DownloadOutlined,
  SettingOutlined,
} from '@ant-design/icons-vue'

// 快速搜索相关
import QuickSearchTabbar from '@/components/QuickSearchTabbar.vue'
// 定义标签数据
const tabsData = ref([
  {
    name: '全部',
    icon: ProfileFilled,
    active: true,
  },
  {
    name: '看板1',
    icon: ProfileFilled,
    active: false,
  },
  {
    name: '看板2',
    icon: ProfileFilled,
    active: false,
  },
])
// 事件处理函数
const handleTabClick = (tab: any, index: number) => {
  // 更新选中状态
  tabsData.value.forEach((item, i) => {
    item.active = i === index
  })
  console.log('Tab clicked:', tab, index)
}

// 二级分组选择器
import SecondaryGroupingSelector from '@/components/SecondaryGroupingSelector.vue'
// 定义分组数据
const groupingItems = ref([
  { label: '全部', value: 'all' },
  { label: '压铸', value: 'casting' },
  { label: '去毛刺', value: 'deburring' },
  { label: '车', value: 'turning' },
  { label: '钻', value: 'drilling' },
  { label: '铣', value: 'milling' },
  { label: '氧化', value: 'oxidation' },
  { label: '喷涂', value: 'painting' },
  { label: '包装入库', value: 'packaging' },
])
const handleGroupingChange = (value: any) => {
  console.log('Grouping changed:', value)
}

// 高级搜索输入框
import AdvancedSearchInput from '@/components/AdvancedSearchInput.vue'
// 搜索相关数据
const searchQuery = ref('')
const searchOptions = ref([
  {
    label: '工单编号、产品编号、产品名称、产品规格',
    value: 'all',
    placeholder: '输入产品编号搜索',
  },
  { label: '工单编号', value: 'workOrderNo', placeholder: '输入工单编号搜索' },
  { label: '产品编号', value: 'productNo', placeholder: '输入产品编号搜索' },
  { label: '产品名称', value: 'productName', placeholder: '输入产品名称搜索' },
  { label: '产品规格', value: 'productSpec', placeholder: '输入产品规格搜索' },
  { label: '工单备注', value: 'workOrderNote', placeholder: '输入工单备注搜索' },
  { label: '紧急程度', value: 'urgency', placeholder: '输入紧急程度搜索' },
  { label: '压铸机', value: 'machine', placeholder: '输入压铸机搜索' },
])
const handleSearchTypeChange = (type: string) => {
  console.log('Search type changed:', type)
}
const handleSearch = (data: { type: string; value: string }) => {
  console.log('Search triggered:', data)
  // 这里可以调用API进行搜索
}

// 高级搜索表单
import AdvancedSearchForm from '@/components/AdvancedSearchForm/index.vue'

// 表格相关
import { type TableColumn, tableColumns } from './tableColumns'
import ColumnConfigPanel from './components/ColumnConfigPanel.vue'
// import ColumnConfigPanel from '@/components/ColumnConfigPanel/index.vue'
const dataSource = ref([])
const columnSettings = ref<TableColumn[]>(tableColumns)
const columnConfigVisible = ref(false)

// 处理列配置更新
const handleColumnsUpdate = (newColumns: TableColumn[]) => {
  columnSettings.value = newColumns
}

// 关闭字段配置菜单
const closeColumnConfig = () => {
  columnConfigVisible.value = false
}

// 计算可见列，按 order 排序
const visibleColumns = computed(() =>
  columnSettings.value
    .filter((col) => col.visible)
    .sort((a, b) => a.order - b.order)
    .map((col) => ({
      ...col,
      fixed: col.fixed === false ? undefined : col.fixed,
    })),
)
const setRowStyle = () => {
  return {
    style: {
      height: '64px', // 设置行高
    },
  }
}
</script>

<style src="./index.scss" scoped></style>
