<template>
  <div class="column-config-panel" @click.stop>
    <div class="panel-header">
      <div class="panel-title">
        <span class="title-icon"> 字段配置 </span>
        <span class="field-count">{{ visibleCount }}/{{ totalCount }}</span>
      </div>
      <a-input
        v-model:value="searchKeyword"
        placeholder="搜索字段"
        size="small"
        class="search-input"
      >
        <template #prefix>
          <SearchOutlined />
        </template>
      </a-input>
    </div>

    <div class="panel-body">
      <draggable
        v-model="sortedColumns"
        item-key="key"
        handle=".drag-handle"
        @end="handleDragEnd"
        @start="handleDragStart"
        class="column-list"
        @click.stop
        :disabled="false"
        :move="checkMove"
      >
        <template #item="{ element: column }">
          <div v-show="shouldShowColumn(column)" class="column-item">
            <!-- 拖拽手柄 -->
            <div class="drag-handle" :class="{ 'drag-handle--disabled': isColumnFixed(column) }">
              <HolderOutlined />
            </div>

            <!-- 字段图标和名称 -->
            <div class="column-info">
              <span class="column-title">{{ column.title }}</span>
            </div>

            <!-- 固定列按钮 -->
            <div
              class="pin-button"
              :class="{
                'pin-button--active': isColumnFixed(column),
                'pin-button--disabled': !column.visible,
              }"
              @click.stop="!column.visible ? null : toggleColumnFixed(column)"
            >
              <PushpinOutlined />
            </div>

            <!-- 显隐控制按钮 -->
            <div class="visibility-button" @click.stop="toggleColumnVisibility(column)">
              <EyeOutlined v-if="column.visible" />
              <EyeInvisibleOutlined v-else />
            </div>
          </div>
        </template>
      </draggable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import draggable from 'vuedraggable'
import {
  SearchOutlined,
  HolderOutlined,
  PushpinOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
} from '@ant-design/icons-vue'
import type { TableColumn } from '../tableColumns'

interface Props {
  columns: TableColumn[]
}

interface Emits {
  (e: 'update:columns', columns: TableColumn[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 搜索关键词
const searchKeyword = ref('')

// 排序后的列数据（排除操作列）
const sortedColumns = computed({
  get: () =>
    [...props.columns]
      .filter((col) => col.key !== 'action') // 过滤掉操作列
      .sort((a, b) => a.order - b.order),
  set: (newColumns: TableColumn[]) => {
    // 获取操作列
    const actionColumn = props.columns.find((col) => col.key === 'action')

    // 更新非操作列的排序
    const updatedNonActionColumns = newColumns.map((col, index) => ({
      ...col,
      order: index + 1,
    }))

    // 合并操作列和其他列
    const allColumns = actionColumn
      ? [...updatedNonActionColumns, actionColumn]
      : updatedNonActionColumns

    emit('update:columns', allColumns)
  },
})

// 统计信息（排除操作列）
const totalCount = computed(() => props.columns.filter((col) => col.key !== 'action').length)
const visibleCount = computed(
  () => props.columns.filter((col) => col.key !== 'action' && col.visible).length,
)

// 判断是否应该显示列
const shouldShowColumn = (column: TableColumn) => {
  if (!searchKeyword.value) return true
  return column.title.toLowerCase().includes(searchKeyword.value.toLowerCase())
}

// 判断列是否固定
const isColumnFixed = (column: TableColumn) => {
  return column.fixed === 'left' || column.fixed === 'right'
}

// 切换列固定状态
const toggleColumnFixed = (column: TableColumn) => {
  // 如果列不可见，不允许设置固定
  if (!column.visible) {
    return
  }

  // 不允许对操作列进行固定状态切换（虽然操作列已经不在配置面板中显示）
  if (column.key === 'action') {
    return
  }

  const currentColumns = [...props.columns]
  const targetColumnIndex = currentColumns.findIndex((col) => col.key === column.key)
  if (targetColumnIndex === -1) return

  const targetColumn = currentColumns[targetColumnIndex]
  const isCurrentlyFixed = isColumnFixed(targetColumn)

  // 获取操作列
  const actionColumn = currentColumns.find((col) => col.key === 'action')
  // 获取非操作列
  const nonActionColumns = currentColumns.filter((col) => col.key !== 'action')

  let updatedNonActionColumns = [...nonActionColumns]

  if (isCurrentlyFixed) {
    // 取消固定：将列的fixed设置为false
    const targetIndex = updatedNonActionColumns.findIndex((col) => col.key === column.key)
    if (targetIndex !== -1) {
      updatedNonActionColumns[targetIndex] = {
        ...updatedNonActionColumns[targetIndex],
        fixed: false,
      }
    }

    // 重新排序：固定列在前，非固定列在后
    const fixedColumns = updatedNonActionColumns.filter((col) => isColumnFixed(col))
    const unfixedColumns = updatedNonActionColumns.filter((col) => !isColumnFixed(col))

    // 按原有order排序
    fixedColumns.sort((a, b) => a.order - b.order)
    unfixedColumns.sort((a, b) => a.order - b.order)

    // 重新组合并更新order
    updatedNonActionColumns = [...fixedColumns, ...unfixedColumns].map((col, index) => ({
      ...col,
      order: index + 1,
    }))
  } else {
    // 设置固定：将列设置为固定状态
    const targetIndex = updatedNonActionColumns.findIndex((col) => col.key === column.key)
    if (targetIndex !== -1) {
      updatedNonActionColumns[targetIndex] = {
        ...updatedNonActionColumns[targetIndex],
        fixed: 'left' as const, // 非操作列都固定在左侧
      }
    }

    // 重新排序：固定列在前，非固定列在后
    const fixedColumns = updatedNonActionColumns.filter((col) => isColumnFixed(col))
    const unfixedColumns = updatedNonActionColumns.filter((col) => !isColumnFixed(col))

    // 按原有order排序
    fixedColumns.sort((a, b) => a.order - b.order)
    unfixedColumns.sort((a, b) => a.order - b.order)

    // 重新组合并更新order
    updatedNonActionColumns = [...fixedColumns, ...unfixedColumns].map((col, index) => ({
      ...col,
      order: index + 1,
    }))
  }

  // 合并操作列和其他列
  const allColumns = actionColumn
    ? [...updatedNonActionColumns, actionColumn]
    : updatedNonActionColumns

  emit('update:columns', allColumns)
}

// 切换列显隐状态
const toggleColumnVisibility = (column: TableColumn) => {
  // 不允许对操作列进行显隐切换（虽然操作列已经不在配置面板中显示）
  if (column.key === 'action') {
    return
  }

  const updatedColumns = props.columns.map((col) => {
    if (col.key === column.key) {
      const newCol = { ...col, visible: !col.visible }
      // 如果列被隐藏，同时取消固定状态
      if (!newCol.visible && isColumnFixed(newCol)) {
        newCol.fixed = false
      }
      return newCol
    }
    return col
  })
  emit('update:columns', updatedColumns)
}

// 检查拖拽移动是否允许
const checkMove = (evt: any) => {
  // 不允许拖拽固定列和操作列
  const draggedElement = evt.draggedContext.element
  return !isColumnFixed(draggedElement) && draggedElement.key !== 'action'
}

// 拖拽开始处理
const handleDragStart = (evt: any) => {
  // 如果是固定列或操作列，阻止拖拽
  const draggedElement = evt.item.__vueParentComponent?.ctx?.element
  if (draggedElement && (isColumnFixed(draggedElement) || draggedElement.key === 'action')) {
    evt.preventDefault()
    return false
  }
}

// 拖拽结束处理
const handleDragEnd = () => {
  // 拖拽结束后，sortedColumns 的 setter 会自动触发
}
</script>

<style scoped lang="scss">
.column-config-panel {
  width: 300px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;

    .panel-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      font-weight: 500;
      font-size: 14px;

      .title-icon {
        font-size: 16px;
      }

      .field-count {
        color: #666;
        font-size: 12px;
        background: #f5f5f5;
        padding: 2px 6px;
        border-radius: 4px;
        margin-left: auto;
      }
    }

    .search-input {
      width: 100%;
    }
  }

  .panel-body {
    max-height: 450px;
    overflow-y: auto;

    .column-list {
      padding: 8px 12px;
    }

    .column-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 4px;
      border-radius: 6px;
      transition: all 0.2s;
      border: 1px solid transparent;
      margin-bottom: 4px;

      &:hover {
        background: #fafafa;
      }

      .drag-handle {
        cursor: grab;
        color: #666;
        padding: 2px;
        font-size: 14px;

        &:hover {
          color: #8c8c8c;
        }

        &:active {
          cursor: grabbing;
        }

        &--disabled {
          cursor: not-allowed;
          color: #d9d9d9;

          &:hover {
            color: #d9d9d9;
          }
        }
      }

      .column-info {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 10px;
        min-width: 0;

        .column-icon {
          color: #8c8c8c;
          font-size: 16px;
          flex-shrink: 0;
        }

        .column-title {
          font-size: 14px;
          color: #262626;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .pin-button {
        color: #666;
        padding: 4px;
        border-radius: 4px;
        cursor: pointer;

        &--active {
          color: $primary-color;
        }

        &--disabled {
          color: #d9d9d9;
          cursor: not-allowed;
        }
      }

      .visibility-button {
        color: #666;
        padding: 4px;
        border-radius: 4px;
        cursor: pointer;
      }
    }
  }
}

// 滚动条样式
.panel-body::-webkit-scrollbar {
  width: 6px;
}

.panel-body::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.panel-body::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;

  &:hover {
    background: #bfbfbf;
  }
}
</style>
