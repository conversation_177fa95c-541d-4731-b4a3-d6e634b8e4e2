# CollapsibleSearchLayout 可折叠搜索条件布局组件使用指南

## 概述

`CollapsibleSearchLayout` 是一个基于 Vue 3 和 Ant Design Vue 的可折叠搜索条件布局组件，专为工业数字化工单管理系统设计。该组件提供了灵活的搜索条件布局，支持响应式设计和展开/收起功能。

## 主要特性

### ✅ 已实现的功能

1. **可折叠布局**
   - 默认显示前3个搜索条件
   - 超过3个条件时显示展开/收起按钮
   - 平滑的展开/收起动画效果

2. **响应式Grid布局**
   - 基于 Ant Design Vue 的 Row/Col 组件
   - 默认每行显示3个搜索条件
   - 支持自定义每行显示数量

3. **灵活的插槽系统**
   - 支持任意类型的搜索表单控件
   - 每个搜索条件都有独立的插槽
   - 支持复杂的表单组件组合

4. **智能按钮布局**
   - 查询按钮自动定位到合适位置
   - 展开/收起按钮带有清晰的视觉指示
   - 按钮组合理布局，不占用过多空间

5. **TypeScript 支持**
   - 完整的类型定义
   - 良好的开发体验和代码提示

## 快速开始

### 1. 基础用法

```vue
<template>
  <CollapsibleSearchLayout
    :conditions="searchConditions"
    @search="handleSearch"
  >
    <template #condition-0>
      <a-form-item label="工单编号">
        <a-input v-model:value="form.workOrderNo" placeholder="请输入工单编号" />
      </a-form-item>
    </template>
    
    <template #condition-1>
      <a-form-item label="产品名称">
        <a-input v-model:value="form.productName" placeholder="请输入产品名称" />
      </a-form-item>
    </template>
    
    <template #condition-2>
      <a-form-item label="状态">
        <a-select v-model:value="form.status" placeholder="请选择状态">
          <a-select-option value="pending">待处理</a-select-option>
          <a-select-option value="completed">已完成</a-select-option>
        </a-select>
      </a-form-item>
    </template>
  </CollapsibleSearchLayout>
</template>

<script setup>
import CollapsibleSearchLayout from '@/components/CollapsibleSearchLayout'

const searchConditions = ref([
  { label: '工单编号' },
  { label: '产品名称' },
  { label: '状态' },
])

const form = ref({
  workOrderNo: '',
  productName: '',
  status: '',
})

const handleSearch = () => {
  console.log('搜索参数:', form.value)
  // 执行搜索逻辑
}
</script>
```

### 2. 高级配置

```vue
<template>
  <CollapsibleSearchLayout
    :conditions="advancedConditions"
    :default-expanded="false"
    :items-per-row="4"
    :min-items-to-show-toggle="5"
    @search="handleAdvancedSearch"
    @toggle="handleToggle"
  >
    <!-- 搜索条件插槽 -->
  </CollapsibleSearchLayout>
</template>
```

## 组件API

### Props

| 参数 | 说明 | 类型 | 默认值 | 必填 |
|------|------|------|--------|------|
| conditions | 搜索条件配置数组 | `SearchCondition[]` | `[]` | 是 |
| defaultExpanded | 默认是否展开所有条件 | `boolean` | `false` | 否 |
| itemsPerRow | 每行显示的搜索条件数量 | `number` | `3` | 否 |
| minItemsToShowToggle | 显示展开/收起按钮的最小条件数 | `number` | `3` | 否 |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| search | 点击查询按钮时触发 | - |
| toggle | 展开/收起状态改变时触发 | `(expanded: boolean)` |

### Slots

| 插槽名 | 说明 | 作用域参数 |
|--------|------|------------|
| condition-{index} | 第{index}个搜索条件的内容 | `{ condition, index }` |

## 实际应用示例

### 工单管理页面

```vue
<template>
  <div class="work-order-page">
    <!-- 搜索区域 -->
    <div class="search-section">
      <CollapsibleSearchLayout
        :conditions="workOrderConditions"
        @search="searchWorkOrders"
        @toggle="onSearchToggle"
      >
        <!-- 工单编号 -->
        <template #condition-0>
          <a-form-item label="工单编号" class="search-form-item">
            <a-input 
              v-model:value="searchForm.workOrderNo" 
              placeholder="请输入工单编号"
              allow-clear
            />
          </a-form-item>
        </template>

        <!-- 产品编号 -->
        <template #condition-1>
          <a-form-item label="产品编号" class="search-form-item">
            <a-input 
              v-model:value="searchForm.productNo" 
              placeholder="请输入产品编号"
              allow-clear
            />
          </a-form-item>
        </template>

        <!-- 工单状态 -->
        <template #condition-2>
          <a-form-item label="工单状态" class="search-form-item">
            <a-select 
              v-model:value="searchForm.status" 
              placeholder="请选择工单状态"
              allow-clear
            >
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="pending">待处理</a-select-option>
              <a-select-option value="processing">处理中</a-select-option>
              <a-select-option value="completed">已完成</a-select-option>
            </a-select>
          </a-form-item>
        </template>

        <!-- 创建时间范围 -->
        <template #condition-3>
          <a-form-item label="创建时间" class="search-form-item">
            <a-range-picker 
              v-model:value="searchForm.createTimeRange" 
              style="width: 100%"
            />
          </a-form-item>
        </template>

        <!-- 负责人 -->
        <template #condition-4>
          <a-form-item label="负责人" class="search-form-item">
            <a-select 
              v-model:value="searchForm.assignee" 
              placeholder="请选择负责人"
              allow-clear
              show-search
            >
              <a-select-option 
                v-for="user in userList" 
                :key="user.id" 
                :value="user.id"
              >
                {{ user.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </template>
      </CollapsibleSearchLayout>
    </div>

    <!-- 结果展示区域 -->
    <div class="result-section">
      <!-- 表格或其他内容 -->
    </div>
  </div>
</template>
```

## 样式定制

组件使用项目的 SCSS 变量系统，可以通过修改变量来定制样式：

```scss
// 在你的样式文件中
.my-search-layout {
  :deep(.collapsible-search-layout) {
    .search-button-col {
      .button-group {
        justify-content: flex-start; // 改变按钮对齐方式
      }
    }
    
    .toggle-button {
      color: #custom-color; // 自定义展开按钮颜色
    }
  }
}
```

## 最佳实践

### 1. 搜索条件数量建议
- **3个以下**: 不显示展开/收起功能，直接展示所有条件
- **3-8个**: 推荐使用默认配置，前3个条件默认显示
- **8个以上**: 考虑将搜索条件分组或使用其他UI模式

### 2. 表单验证
```vue
<script setup>
const validateAndSearch = () => {
  // 在搜索前进行表单验证
  if (!searchForm.value.workOrderNo && !searchForm.value.productName) {
    message.warning('请至少输入一个搜索条件')
    return
  }
  
  // 执行搜索
  handleSearch()
}
</script>
```

### 3. 搜索条件重置
```vue
<template>
  <div class="search-actions">
    <a-space>
      <a-button @click="resetSearchForm">重置</a-button>
      <a-button type="primary" @click="handleSearch">搜索</a-button>
    </a-space>
  </div>
</template>

<script setup>
const resetSearchForm = () => {
  searchForm.value = {
    workOrderNo: '',
    productName: '',
    status: '',
    // ... 其他字段
  }
}
</script>
```

## 演示页面

项目中提供了两个演示页面：

1. **基础演示**: `/demo` - 展示组件的基本功能和用法
2. **完整示例**: `/search-example` - 展示在实际工单管理场景中的应用

## 技术支持

如果在使用过程中遇到问题，请参考：

1. 组件源码: `src/components/CollapsibleSearchLayout/`
2. 演示代码: `src/views/demo/`
3. API文档: `src/components/CollapsibleSearchLayout/README.md`

## 更新日志

### v1.0.0 (2024-01-15)
- ✅ 初始版本发布
- ✅ 支持可折叠搜索条件布局
- ✅ 响应式Grid布局
- ✅ TypeScript支持
- ✅ 完整的演示和文档
